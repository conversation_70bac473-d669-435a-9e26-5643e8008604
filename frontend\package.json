{"name": "document-analysis-frontend", "version": "1.0.0", "description": "Document Intelligence Analysis System Frontend", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.6.5", "dayjs": "^1.11.13", "element-plus": "^2.4.4", "element-resize-detector": "^1.2.4", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "moment": "^2.30.1", "pinia": "^2.1.7", "uuid": "^11.1.0", "vue": "^3.4.15", "vue-router": "^4.2.5", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.4", "sass": "^1.90.0", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.3", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11", "vite-plugin-windicss": "^1.9.4", "vue-tsc": "^1.8.27", "windicss": "^3.5.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}