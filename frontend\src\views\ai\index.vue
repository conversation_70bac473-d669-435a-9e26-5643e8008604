<template>
  <div class="chat-box">
    <!-- 智能链接 -->
    <div class="chat-link">
      <div class="link-box">
        <div class="link-top">
          <div class="imgs flex flex-nowrap">
            <img src="@/assets/images/logo.gif" alt="" class="link-logo" />
            <div class="introduce">
              <div class="mt-5px">HI，我是安徽交易集团智能客服小一 一为您提供公共资源交易服务</div>
            </div>
          </div>
        </div>
        <div class="shortcut-menu">
          <div class="menu-title">联系方式</div>
          <div class="menu-content">
            <el-scrollbar height="100%" class="px-20px">
              <div class="menu-item" v-for="item in menuList" :key="item.id">
                <a :href="item.url" target="_blank">
                  <div class="flex items-center">
                    <img src="@/assets/images/position.png" alt="" class="w-36px" />
                    <span>{{ item.name }}</span>
                  </div>
                </a>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <!-- 智能客服问答 -->
    <div class="chat-content">
      <!-- 聊天框 -->
      <div class="chat-wrap">
        <div class="chat-wrap__main">
          <div class="chat-wrap__main-content" :style="{ paddingBottom: `${chatMainMrgBottom}px` }">
            <el-scrollbar ref="scrollbarRef" class="w-full" height="100%" always>
              <ClientChat :list="chatList" @send="onSendQuestion" @loadMore="loadMoreHistory" />
            </el-scrollbar>
          </div>
          <div class="chat-wrap__main-footer">
            <QuestionInput @send="onSendQuestion" />
          </div>
        </div>
      </div>
    </div>
    <!-- 常见问题与历史记录 -->
    <div class="chat-history">
      <Problems @select="onSelectProblem" />
    </div>
  </div>
</template>

<script setup lang="ts" name="AICustomerService">
import { ref, provide, onMounted } from 'vue'
import ClientChat from './components/client-chat.vue'
import QuestionInput from './components/question-input.vue'
import Problems from './components/problem.vue'
import { getToken } from '@/api/maxkb'
import { getHistory } from '@/api/chat'
import { getAccessToken, setToken } from '@/utils/auth'

// 创建 scrollbar 引用
const scrollbarRef = ref()

// 提供给子组件
provide('scrollbarRef', scrollbarRef)

const menuList = ref([
  {
    id: 1,
    name: '安徽公共资源交易集团',
    url: 'https://www.ahggzyjt.com/'
  },
  {
    id: 2,
    name: '安徽公共资源交易集团项目管理有限公司',
    url: 'https://www.ahggzyjt.com/'
  },
  {
    id: 3,
    name: '安徽公共资源交易集团科技有限公司',
    url: 'https://www.huiyicai.cn/'
  },
  {
    id: 4,
    name: '合肥市产权交易中心',
    url: 'https://www.haee.com.cn/'
  },
  {
    id: 5,
    name: '安徽省农村综合产权交易所',
    url: 'https://www.ahaee.com/'
  },
  {
    id: 6,
    name: '安徽联合技术产权交易所',
    url: 'https://www.ahtre.com/'
  }
])

// 其他必要的状态和方法
const chatList = ref([])
const chatMainMrgBottom = ref(0)

// 发送问题的处理函数
const onSendQuestion = (question: string) => {
  // 处理发送问题的逻辑
  console.log('发送问题:', question)
}

// 加载更多历史记录的处理函数
const loadMoreHistory = () => {
  // 处理加载更多历史记录的逻辑
  console.log('加载更多历史记录')
}
//获取token
const getChatToken = async () => {
  try {
    //
    console.log('获取token')
    console.log(getAccessToken())
    if (getAccessToken()) return
    const data = await getToken()
    console.log(data)
    setToken(data)
  } catch (error: any) {
    console.error('获取token失败:', error)
  } finally {
    getHistoryList()
  }
}
const getHistoryList = async () => {
  try {
    const data = await getHistory()
    console.log(data)
    debugger
  } catch (error: any) {
    console.error('获取失败:', error)
  } finally {
  }
}
onMounted(() => {
  getChatToken()
})
</script>

<style scoped lang="scss">
.chat-box {
  width: 100%;
  height: 100vh;
  display: flex;
  position: relative;
  background: url('@/assets/images/bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: 50%;
  box-sizing: border-box;
  .chat-content {
    flex: 1;
    height: 100%;
    padding: 30px 60px 0;
    box-sizing: border-box;
  }
  .chat-history {
    lex-shrink: 0;
    width: 300px;
    height: 100%;
    // border-radius: 24px 0 0 24px;
    // border: 2px solid #fff;
    padding: 20px 0;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: -12px 0px 6px rgba(146, 221, 252, 0.1);
  }
}
.chat-link {
  height: 100%;
  padding: 30px 0 30px 30px;
  .link-box {
    height: 100%;
    flex-shrink: 0;
    width: 380px;
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.3), hsla(0, 0%, 100%, 0.5));
    box-shadow: 0 0 10px 0 rgba(215, 0, 0, 0.1);
    border-radius: 16px 16px 16px 16px;
    border: 2px solid hsla(0, 0%, 100%, 0.8);
    padding: 20px 0;
    .link-top {
      padding: 0 20px;
    }
    .menu-content {
      height: calc(100vh - 410px);
    }
  }
  .link-logo {
    width: 130px;
  }
  .introduce {
    flex-shrink: 0;
    width: 210px;
    height: 142px;
    background: url('@/assets/images/tip-bg.png') no-repeat;
    background-size: 200px auto;
    background-position: 50%;
    color: rgba(5, 86, 222, 1);
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    font-size: 12px;
  }
  .menu-title {
    margin-top: 30px;
    padding: 20px;
    font-size: 26px;
    background: linear-gradient(90deg, rgba(42, 130, 228, 1), rgba(9, 205, 213, 1));
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }
  .menu-item {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: url('@/assets/images/menu-bg.png') no-repeat center;
    background-size: 100% 100%;
    font-size: 16px;
    color: rgba(1, 42, 246, 0.45);
  }
}
.chat-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &__main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: hidden;

    &-content {
      height: calc(100vh - 160px);
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
    }
    &-footer {
      position: relative;
      z-index: 3;
    }
  }
}
</style>
