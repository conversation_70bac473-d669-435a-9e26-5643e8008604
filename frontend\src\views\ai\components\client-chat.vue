<template>
  <div class="client-chat" style="height: 100%">
    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <el-button v-if="!loading" type="primary" link @click="loadMore">加载更多</el-button>
      <el-icon v-else class="is-loading"><Loading /></el-icon>
    </div>
    <div v-if="msgList.length > 0">
      <div class="qa-item" v-for="(item, index) in msgList" :key="index">
        <!-- 时间戳 -->
        <div class="timestamp" v-if="index === 0 || (index !== 0 && item.timestamp && Number(item.timestamp) - Number(msgList[index - 1].timestamp) > timestampGap)">
          {{ formatTime(item.timestamp) }}
        </div>

        <!-- 问题 -->
        <div class="question-item" v-if="item.is_from_self || item.replyMethod == 0">
          <el-icon v-if="item.is_loading" class="qs-loading is-loading"><Loading /></el-icon>

          <div class="question-text" style="max-width: 600px" v-html="renderMarkdown(item.content)" />
          <div class="question-avatar">
            <img class="user-avatar" src="@/assets/images/user.png" />
          </div>
        </div>
        <!-- 答案 -->
        <div class="answer-item" v-else>
          <!-- 头像 -->
          <div class="answer-avatar">
            <img class="robot-avatar" :src="item.fromAvatar || item.from_avatar" />
          </div>
          <!-- 答案信息 -->
          <div class="answer-info" :ref="el => setItemRef(el, item.record_id)">
            <div class="loading" v-if="item.loading_message">正在思考中</div>
            <!-- Markdown渲染 -->
            <div class="answer-md" style="max-width: 600px" v-html="renderMarkdown(item.content)" />
          </div>
        </div>
      </div>
    </div>
    <!-- 无数据 -->
    <div v-else class="no-data flex h-full flex-col justify-center pb-60px">
      <div class="top flex items-center justify-center mt-20px">
        <img src="@/assets/images/title.png" alt="" />
      </div>
      <h3 class="introduce flex items-center justify-center">您好!很高兴为您服务，有什么问题您可以问我哦，我会尽力帮您解答!</h3>
      <div class="ai-introduce-box">
        <div class="introduce-info">
          <p>【全天候服务】公共资源交易智能客服，7x24小时在线答疑,开启人机交互全新时代~</p>
          <p>【覆盖全面】政策法规、流程指引、业务办理，一键咨询,秒速响应，让公共资源交易更省心、更便捷!~</p>

          <img src="@/assets/images/divice.png" alt="" class="w-full my-20px" />
          <div class="introduce-bottom flex items-center justify-between">
            <ul class="pt-8px">
              <li class="flex items-center pb-20px">
                <img src="@/assets/images/icon1.png" alt="" class="w-64px" />
                <div class="ml-10px">
                  <div class="text-black font-bold text-18px">办事指南</div>
                  <div class="mt-2px">政策明晰，AI 速答，流程易懂，指引精准。</div>
                </div>
              </li>
              <li class="flex items-center pb-20px">
                <img src="@/assets/images/icon2.png" alt="" class="w-64px" />
                <div class="ml-10px">
                  <div class="text-black font-bold text-18px">信息检索</div>
                  <div class="mt-2px">通过自然语言处理等技术，实现信息的智能检索和精准推送!</div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ClientChat" lang="ts">
import { inject } from 'vue'
import { cloneDeep } from 'lodash-es'
import MarkdownIt from 'markdown-it'
import moment from 'moment'
import elementResizeDetectorMaker from 'element-resize-detector'
import { scrollToBottom } from '@/utils/util'
import { MESSAGE_TYPE, ACCESS_TYPE } from '@/constants'

import { sseCls } from '@/utils/sse'
// 初始化 markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: false,
  breaks: true,
  typographer: true
})

// 添加链接新窗口打开
const defaultRender =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }
md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  tokens[idx].attrPush(['target', '_blank'])
  return defaultRender(tokens, idx, options, env, self)
}

const props = defineProps({
  // 如果需要的话可以定义props
  list: {
    type: Array,
    default: () => []
  }
})

// 新增 emit 定义
const emit = defineEmits(['loadMore'])

// 新增响应式状态
const loading = ref(false)
const historyLoading = ref(false)
const timestampGap = ref(5 * 60) // 两次问题间隔大于5min，则展示时间戳
const msgList = ref([])
const robotName = ref('')
const chatBoxHeight = ref(document.body.clientHeight)
const jsScrolling = ref(false)
const userScrolling = ref(false)
const hasMore = ref(false)
const isFirstLoad = ref(true)

// 事件总线
const eventHub = inject('eventHub')

// 获取父组件的 scrollbar 实例
const scrollbarRef = inject('scrollbarRef', null)

// 工具方法
const formatTime = timestamp => {
  return moment(new Date(String(timestamp).length === 10 ? timestamp * 1000 : Number(timestamp))).format('MM-DD HH:mm')
}

// 安全的滚动操作函数
const safeScrollToBottom = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
    } catch (error) {
      console.warn('滚动操作失败:', error)
    }
  }
}

// 安全的滚动事件监听器添加/移除
const addScrollListener = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.wrapRef.addEventListener('scroll', handleScroll)
    } catch (error) {
      console.warn('添加滚动监听器失败:', error)
    }
  }
}

const removeScrollListener = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.wrapRef.removeEventListener('scroll', handleScroll)
    } catch (error) {
      console.warn('移除滚动监听器失败:', error)
    }
  }
}

const renderMarkdown = content => {
  return md.render(content || '')
}

const setItemRef = (el, recordId) => {
  if (el) {
    el.setAttribute('id', recordId)
  }
}

// 监听用户端/管理端体验侧的ws事件
const listenClientAndManageEvent = () => {
  // 从缓存获取机器人信息
  let cachedConfig = null
  if (ACCESS_TYPE === 'sse') {
    cachedConfig = sseCls.sseQueryConfigInfo()
  } else {
  }
  if (cachedConfig) {
    robotName.value = cachedConfig.name
  }

  // 监听答案消息队列变更事件
  eventHub.on('client_msgContentChange', res => {
    const { chatsContent, type } = res
    renderMsgList(chatsContent, type)
  })
}

// 监听公共的ws事件
const listenCommonEvent = () => {
  eventHub.on('data_history', () => {
    historyLoading.value = false
  })

  eventHub.on('data_historyError', () => {
    historyLoading.value = false
  })
}

// 渲染消息会话页面
const renderMsgList = (data, type) => {
  const noScrollEvt = [MESSAGE_TYPE.HISTORY, MESSAGE_TYPE.STOP, MESSAGE_TYPE.WORKBENCH_HISTORY, MESSAGE_TYPE.FEEDBACK]
  const list = data.map(el => ({ ...el, showPop: true }))
  msgList.value = cloneDeep(list)

  nextTick(() => {
    // 使用 Element Plus scrollbar 的滚动方法
    if (!userScrolling.value && !noScrollEvt.includes(type)) {
      jsScrolling.value = true
      // 滚动到底部 - 使用安全函数
      safeScrollToBottom()
    }
    if (msgList.value.length > 0 && msgList.value[msgList.value.length - 1].is_final === true) {
      userScrolling.value = false
    }
  })
}

// 监听滚动事件
const handleScroll = () => {
  if (!scrollbarRef.value || !scrollbarRef.value.wrapRef) return

  const { scrollTop } = scrollbarRef.value.wrapRef
  // 当滚动到顶部时触发加载更多
  if (scrollTop === 0 && !loading.value && hasMore.value) {
    loadMore()
  }
}

// 加载更多历史记录
const loadMore = async () => {
  if (loading.value) return
  loading.value = true
  emit('loadMore')
}

// 监听消息列表变化
watch(
  () => props.list,
  (newList, oldList) => {
    loading.value = false
    // 判断是否还有更多数据 - 如果数据条数是20的倍数，说明可能还有更多数据
    hasMore.value = newList.length > 0 && newList.length % 20 === 0 && newList.length != oldList.length

    // 只在首次加载时滚动到底部
    if (isFirstLoad.value) {
      isFirstLoad.value = false
      nextTick(() => {
        safeScrollToBottom()
      })
    }
  }
)

onMounted(() => {
  listenClientAndManageEvent()
  listenCommonEvent()

  const erd = elementResizeDetectorMaker()
  const bodyDom = document.body

  erd.listenTo(bodyDom, element => {
    chatBoxHeight.value = element.clientHeight - 113
  })

  // 监听滚动事件 - 使用 nextTick 确保 DOM 已渲染
  nextTick(() => {
    addScrollListener()
  })
})

onUnmounted(() => {
  // 清理事件监听
  eventHub.off('client_msgContentChange')
  eventHub.off('data_history')
  eventHub.off('data_historyError')

  // 移除滚动监听
  removeScrollListener()
})
</script>

<style lang="scss">
.client-chat::-webkit-scrollbar {
  display: none;
}
.robot-avatar,
.user-avatar {
  width: 32px;
  height: 32px;
  margin-top: 5px;
  border-radius: 32px;
  vertical-align: middle;
}
.robot-avatar {
  margin-right: 10px;
}
.user-avatar {
  margin-left: 10px;
}

.client-chat {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: overlay;
  padding: 0 12px;

  .loading {
    margin: 1em 0;
    width: 90px;
    &:after {
      content: '.';
      animation: ellipsis 1.5s steps(1, end) infinite;
    }
  }

  @keyframes ellipsis {
    0% {
      content: '.';
    }
    33% {
      content: '..';
    }
    66% {
      content: '...';
    }
    100% {
      content: '.';
    }
  }

  .qa-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 1, 10, 0.93);

    .timestamp {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: rgba(1, 11, 50, 0.41);
      margin: 16px 0;
    }

    .question-item {
      display: flex;
      align-items: center;
      width: fit-content;
      text-align: center;
      align-self: flex-end;
      padding-left: 44px;

      .qs-error {
        min-width: 16px;
        margin-right: 10px;
        color: #f75559;
      }
      .qs-loading {
        margin-right: 10px;
      }
      .question-text {
        background: linear-gradient(90deg, rgba(73, 91, 254, 1) 0%, rgba(46, 148, 255, 1) 100%); // #dbe8ff // var(--bubble-bg-myself-normal);
        border-radius: 6px;
        padding: 0 12px;
        text-align: left;
        word-break: break-all;
        word-wrap: break-word;
        color: #fff;
        p {
          margin: 10px 0;
        }

        code {
          white-space: break-spaces;
        }
        img {
          max-width: 80%;
        }
      }
    }

    .summary-item {
      align-self: center;
      margin: 12px 0;
    }

    .answer-item {
      display: flex;

      .contacter-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .answer-info {
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 6px 12px;
        background: #f4f5f7; // var(--bubble-bg-each-other-normal);
        border-radius: 6px;
        width: fit-content;

        .answer-md {
          font:
            400 14px / 20px -apple-system,
            BlinkMacSystemFont,
            'Segoe UI',
            Roboto,
            'Helvetica Neue',
            Helvetica,
            Arial,
            'PingFang SC',
            'Microsoft YaHei',
            'Noto Sans',
            sans-serif;
          word-break: break-all;
          word-wrap: break-word;
          .table {
            /* 滚动条Track */
            ::-webkit-scrollbar-track {
              background: transparent;
            }

            /* Handle */
            ::-webkit-scrollbar-thumb {
              border-radius: 10px;
              background: rgba(17, 32, 70, 0.13);
            }
          }
          img {
            max-width: 100%;
            cursor: pointer;
          }
          p {
            word-break: break-all;
            word-wrap: break-word;
            margin: 10px 0;
          }
          code {
            white-space: break-spaces;
          }
          table {
            // display: inline-block;
            // white-space: nowrap;
            // max-width: 100%;
            // overflow: scroll;
            // background: white;
            // border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            // border-right: 1px solid rgba(18, 42, 79, 0.08);
            // border-spacing: 0;
            // border-collapse: collapse;
            display: inline-block;
            overflow-x: scroll;
            background: white;
            border-spacing: 0;
            border-collapse: collapse;
            border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            border-right: 1px solid rgba(18, 42, 79, 0.08);
            max-width: 100%;
            th {
              background: #eaecef;
              color: rgba(1, 11, 50, 0.41);
              // padding: 12px;
              // font-weight: 400;
              // background: #eaecef;
            }
            td,
            th {
              border-left: 1px solid rgba(18, 42, 79, 0.08);
              border-top: 1px solid rgba(18, 42, 79, 0.08);
            }
            td {
              padding: 8px 12px;
              min-width: 20px;
            }
          }
          .table-style {
            display: inline-block;
            white-space: nowrap;
            max-width: 100%;
            overflow: scroll;
            background: white;
            border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            border-right: 1px solid rgba(18, 42, 79, 0.08);
            border-spacing: 0;
            border-collapse: collapse;
            th {
              color: rgba(1, 11, 50, 0.41);
              padding: 12px;
              font-weight: 400;
              background: #eaecef;
            }
            td,
            th {
              border-left: 1px solid rgba(18, 42, 79, 0.08);
              border-top: 1px solid rgba(18, 42, 79, 0.08);
            }
            td {
              padding: 8px 4px;
              min-width: 45px;
              overflow-wrap: break-word;
              white-space: break-spaces;
            }
          }
        }
        .answer-expand {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          width: 44px;
          height: 24px;
          margin-bottom: 12px;
          background: #fff;
          box-shadow: var(--shadow-small-light);
          border-radius: 16px;
          align-self: center;
        }
        .stop-ws {
          color: rgba(217, 226, 252, 0.51);
          margin-left: 5px;
        }
        .answer-source {
          margin: 12px 0;
          font-size: 14px;
          color: rgba(217, 226, 252, 0.51);
          text-align: left;

          .v-button {
            text-decoration: none;
            text-align: left;
          }
        }
      }
    }
  }
  .qa-item:last-child {
    padding-bottom: 120px;
  }

  .load-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;

    .el-icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }
  }
}
.no-data {
  .introduce {
    margin: 30px 0 10px 0;
    font-weight: 500;
    font-size: 18px;
    color: rgba(153, 153, 153, 1);
  }
  .ai-introduce-box {
    background-image: url('@/assets/images/ai-introduce.png');
    background-position: 50%;
    background-repeat: no-repeat;
    box-shadow: 0 4px 10px 0 rgba(215, 0, 0, 0.04);
    background-size: 100% 100%;
    .introduce-info {
      padding: 22px 20px 12px;
      color: rgba(102, 102, 102, 1);
      font-size: 15px;
      line-height: 28px;
    }
  }
}
</style>
