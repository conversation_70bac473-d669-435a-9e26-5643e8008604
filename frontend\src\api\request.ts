import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { getAccessToken, removeToken, setToken } from '@/utils/auth'

/**
 * Axios HTTP Client Configuration
 *
 * {{RIPER-5+SMART-6:
 *   Action: "Parallel-Added"
 *   Task_ID: "36f04730-602c-4bac-821c-d46752308e23"
 *   Timestamp: "2025-08-07T12:14:47+08:00"
 *   Authoring_Subagent: "vue-frontend-expert"
 *   Principle_Applied: "SOLID-S (单一职责原则)"
 *   Quality_Check: "编译通过，HTTP客户端配置完整。"
 * }}
 */

// Create axios instance
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    if (getAccessToken() && !isToken) {
      // 'Bearer ' + getAccessToken()
      ;(config as Recordable).headers.Authorization = getAccessToken() // 让每个请求携带自定义token
    }
    // Add loading logic here if needed
    console.log('Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status } = response

    if (status === 200) {
      return data
    } else {
      ElMessage.error(`请求失败: ${status}`)
      return Promise.reject(new Error(`HTTP ${status}`))
    }
  },
  error => {
    console.error('Response Error:', error)

    if (error.response) {
      const { status, data } = error.response
      let message = `请求失败: ${status}`

      if (data?.message) {
        message = data.message
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status === 500) {
        message = '服务器内部错误'
      } else if (status >= 400 && status < 500) {
        message = '请求参数错误'
      }

      ElMessage.error(message)
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export default request
