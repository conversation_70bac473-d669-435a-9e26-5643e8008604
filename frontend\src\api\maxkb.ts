import request from './request'
import type { ApiResponse } from '@/types'

/**
 * MaxKB API - MaxKB智能体相关接口
 *
 * {{RIPER-5+SMART-6:
 *   Action: "Parallel-Added"
 *   Task_ID: "29bf3c22-5039-463c-8779-91dca045ff99"
 *   Timestamp: "2025-08-07T13:30:00+08:00"
 *   Authoring_Subagent: "integration-specialist"
 *   Principle_Applied: "SOLID-S (单一职责原则)"
 *   Quality_Check: "前端API接口定义完整，类型安全。"
 * }}
 */

/**
 * MaxKB分析结果类型定义
 */
export interface MaxKBAnalysisResult {
  documentId: number
  extractResult?: AgentResult
  detectResult?: AgentResult
  status: string
  totalProcessingTime: number
  errorMessage?: string
  processedTime: string
}

export interface AgentResult {
  agentType: string
  chatId: string
  fileInfo: Record<string, any>
  content: string
  rawResponse: Record<string, any>
  processingTime: number
  status: string
  errorMessage?: string
  extractedData?: ExtractedData[]
  detectedIssues?: DetectedIssue[]
}

export interface ExtractedData {
  type: string
  content: string
  position: PositionInfo
  confidence: number
  metadata: Record<string, any>
}

export interface DetectedIssue {
  type: string
  description: string
  severity: string
  position: PositionInfo
  suggestion: string
  metadata: Record<string, any>
}

export interface PositionInfo {
  start: number
  end: number
  page: number
  paragraphIndex: number
  lineNumber: number
}

export interface AnalysisStatus {
  documentId: number
  status: string
  isProcessing: boolean
  isCompleted: boolean
}

export interface SystemStatus {
  maxkbApiStatus: string
  activeAnalyses: number
  totalAnalysesToday: number
  successRate: string
}

/**import type { ApiResponse } from '@/types'
 * 获取token
 */
export const getToken = (): Promise<ApiResponse<string>> => {
  return request.get('/maxkb/qa/token')
}

/**
 * 分析文档
 */
export const analyzeDocument = (
  documentId: number
): Promise<
  ApiResponse<{
    documentId: number
    status: string
    message: string
  }>
> => {
  return request.post(`/maxkb/analyze/${documentId}`)
}

/**
 * 获取分析状态
 */
export const getAnalysisStatus = (documentId: number): Promise<ApiResponse<AnalysisStatus>> => {
  return request.get(`/maxkb/status/${documentId}`)
}

/**
 * 获取分析结果
 */
export const getAnalysisResult = (documentId: number): Promise<ApiResponse<MaxKBAnalysisResult>> => {
  return request.get(`/maxkb/result/${documentId}`)
}

/**
 * 重新分析文档
 */
export const reanalyzeDocument = (
  documentId: number
): Promise<
  ApiResponse<{
    documentId: number
    status: string
    message: string
  }>
> => {
  return request.post(`/maxkb/reanalyze/${documentId}`)
}

/**
 * 取消分析
 */
export const cancelAnalysis = (
  documentId: number
): Promise<
  ApiResponse<{
    documentId: number
    message: string
  }>
> => {
  return request.post(`/maxkb/cancel/${documentId}`)
}

/**
 * 批量分析文档
 */
export const batchAnalyzeDocuments = (
  documentIds: number[]
): Promise<
  ApiResponse<{
    documentIds: number[]
    taskCount: number
    message: string
  }>
> => {
  return request.post('/maxkb/batch-analyze', { documentIds })
}

/**
 * 获取系统状态
 */
export const getSystemStatus = (): Promise<ApiResponse<SystemStatus>> => {
  return request.get('/maxkb/system/status')
}

/**
 * 轮询分析状态
 */
export const pollAnalysisStatus = (
  documentId: number,
  onStatusUpdate: (status: AnalysisStatus) => void,
  onComplete: (result: MaxKBAnalysisResult) => void,
  onError: (error: Error) => void,
  interval: number = 2000
): (() => void) => {
  let isPolling = true

  const poll = async () => {
    if (!isPolling) return

    try {
      const statusResponse = await getAnalysisStatus(documentId)
      if (statusResponse.success) {
        const status = statusResponse.data
        onStatusUpdate(status)

        if (status.isCompleted) {
          isPolling = false

          // 获取最终结果
          try {
            const resultResponse = await getAnalysisResult(documentId)
            if (resultResponse.success) {
              onComplete(resultResponse.data)
            } else {
              onError(new Error(resultResponse.message || '获取分析结果失败'))
            }
          } catch (error) {
            onError(error as Error)
          }
        } else {
          // 继续轮询
          setTimeout(poll, interval)
        }
      } else {
        onError(new Error(statusResponse.message || '获取分析状态失败'))
      }
    } catch (error) {
      onError(error as Error)
    }
  }

  // 开始轮询
  poll()

  // 返回停止轮询的函数
  return () => {
    isPolling = false
  }
}

/**
 * 分析文档并等待结果
 */
export const analyzeDocumentAndWait = (documentId: number, onProgress?: (status: AnalysisStatus) => void): Promise<MaxKBAnalysisResult> => {
  return new Promise((resolve, reject) => {
    // 首先启动分析
    analyzeDocument(documentId)
      .then(response => {
        if (!response.success) {
          reject(new Error(response.message || '启动分析失败'))
          return
        }

        // 开始轮询状态
        pollAnalysisStatus(
          documentId,
          status => {
            if (onProgress) {
              onProgress(status)
            }
          },
          result => {
            resolve(result)
          },
          error => {
            reject(error)
          }
        )
      })
      .catch(reject)
  })
}
