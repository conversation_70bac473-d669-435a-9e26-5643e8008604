<template>
  <div class="question-input" :style="{ width: `${questionInputWith}px`, bottom: `${questionInputBottom}px` }">
    <!-- 停止生成 -->
    <div class="stop-button" v-if="isGeneratingReply">
      <el-button icon="VideoPause" remote @click="onStopStream">停止生成</el-button>
    </div>
    <div v-if="disableInput[currentVisitorId]" class="question-mask"></div>
    <div :class="['question-input-inner', { inputFocus, disabled: isGeneratingReply || isThinking }]">
      <el-input
        type="textarea"
        @focus="updateFocus(true)"
        @blur="updateFocus(false)"
        :readonly="disableInput[currentVisitorId] || isGeneratingReply || isThinking"
        v-model="question"
        @keyup.enter="onPressEnter"
        :placeholder="disableInput[currentVisitorId] ? '已结束会话' : '请输入你的问题'"
        :autofocus="true"
        rows="2"
        class="question-input-inner__textarea"
      ></el-input>
      <div class="question-input-inner__toolbar">
        <div class="toolbar-left"></div>
        <div class="toolbar-right">
          <div class="send-icon" :class="[{ disabled: isSendIconDisabled || disableInput[currentVisitorId] }]">
            <el-tooltip content="发送" placement="bottom">
              <el-button type="primary" round :disabled="isSendIconDisabled || disableInput[currentVisitorId]" @click="onSendQuestion">
                <img src="@/assets/images/send.png" alt="" class="w-20px h-auto" />
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="QuestionInput">
import { ref, watch, onMounted, inject } from 'vue'
import elementResizeDetectorMaker from 'element-resize-detector'
import { scrollToBottom, escapeUserInput } from '@/utils/util'
import { MESSAGE_TYPE, ACCESS_TYPE } from '@/constants'
const { proxy } = getCurrentInstance()
import { sseCls } from '@/utils/sse'

// Props 定义
const props = defineProps({
  currentVisitorId: {
    type: Number,
    default: 0
  }
})

// Emits 定义
const emit = defineEmits(['send'])

// 注入依赖
const eventHub = inject('eventHub')

// 响应式状态
const isSendIconDisabled = ref(true)
const questionInputWith = ref(360)
const questionInputBottom = ref(0)
const question = ref('')
const inputFocus = ref(false)
const disableInput = ref({})
const isGeneratingReply = ref(false)
const isThinking = ref(false)

// 方法定义
const updateFocus = isFocus => {
  inputFocus.value = isFocus
}

const onPressEnter = event => {
  if (event.keyCode === 13) {
    if (!event.metaKey) {
      event.preventDefault()
      onSendQuestion()
    } else {
      question.value = question.value + '\n'
    }
  }
}

const onSendQuestion = () => {
  if (disableInput.value[props.currentVisitorId]) return
  if (!question.value.trim()) return

  emit('send', escapeUserInput(question.value))
  question.value = ''

  // 问题发出后，对话框立即滚动至底部
  nextTick(() => {
    const sDom = document.querySelector('.client-chat')
    if (!sDom) return
    scrollToBottom(sDom, sDom.scrollHeight)
  })
}

const onStopStream = () => {
  if (ACCESS_TYPE === 'sse') {
    sseCls.stopGeneration()
  } else {
  }
  isGeneratingReply.value = false
}

// 监听器
watch(question, val => {
  isSendIconDisabled.value = !val.trim()
})

// 生命周期钩子
onMounted(() => {
  // 监听答案消息队列变更事件
  eventHub.on('client_msgContentChange', res => {
    const { chatsContent, type } = res
    if (type !== MESSAGE_TYPE.ANSWER) return

    isGeneratingReply.value = !!chatsContent.find(r => r.is_final === false)
    isThinking.value = chatsContent.length > 0 && chatsContent[chatsContent.length - 1].loading_message
  })

  const erd = elementResizeDetectorMaker()
  const questionInputParentDom = document.querySelector('.question-input').parentElement
  // 输入框宽度
  erd.listenTo(questionInputParentDom, element => {
    questionInputWith.value = element.clientWidth
  })

  // 输入框bottom间距（坐席端置底无需处理，用户端需计算）
  if (proxy.webIMSource === 'client') {
    const bodyDom = document.body
    const chatWrapperDom = document.querySelector('.chat-wrap__main')

    erd.listenTo(bodyDom, () => {
      questionInputBottom.value = bodyDom.clientHeight > chatWrapperDom.clientHeight ? (bodyDom.clientHeight - chatWrapperDom.clientHeight) / 2 : 0
    })
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  eventHub.off('client_msgContentChange')
})
</script>

<style lang="scss" scoped>
.question-input {
  position: fixed;

  .toolbar-info {
    display: inline-block;
    font-weight: 400;
    font-size: 12px;
    color: rgba(217, 226, 252, 0.51);

    .red-txt {
      color: red;
    }
  }

  .stop-button {
    display: flex;
    justify-content: center;

    button {
      box-shadow:
        0px 0px 1px 0px rgba(18, 19, 25, 0.08),
        0px 0px 2px 0px rgba(18, 19, 25, 0.08),
        0px 2px 4px 0px rgba(18, 19, 25, 0.08);
      border: none;
      padding: 6px 8px;
    }
  }

  .question-mask {
    width: 100%;
    height: 110px;
    position: absolute;
    z-index: 200;
    background: rgba(255, 255, 255, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bolder;
  }

  &-inner {
    display: flex;
    flex-direction: column;
    margin: 12px 0 30px 0;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(17, 32, 70, 0.13);
    border-radius: 10px;
    padding: 12px;
    overflow: hidden;
    padding: 12px;
    background: #fff;

    .v-textarea--default {
      border-radius: 6px;
    }

    &:has(.question-input-inner),
    &:has(.v-textarea--focus),
    &.inputFocus {
      border-color: #409eff;
    }

    &.disabled {
      background-color: rgba(54, 79, 129, 0.04);
      border-color: rgba(189, 200, 255, 0.1) !important;

      .v-textarea__placeholder {
        color: rgba(198, 211, 251, 0.27);
      }
    }

    &__textarea {
      width: 100%;
      max-height: 120px;
      border: none;
      background: none;
      overflow: hidden;

      .v-textarea__txt {
        overflow-y: overlay;
        font-size: 14px;
        font-family:
          -apple-system,
          BlinkMacSystemFont,
          Segoe UI,
          Roboto,
          Helvetica Neue,
          Helvetica,
          Arial,
          PingFang SC,
          Microsoft YaHei,
          Noto Sans,
          sans-serif !important;
      }
    }

    .v-textarea--focus {
      border: none;
    }

    &__toolbar {
      display: flex;
      justify-content: space-between;
      z-index: 10;
      margin: 8px 0 8px 0;

      .v-icon {
        cursor: pointer;
      }

      .toolbar-left {
        display: flex;

        .v-icon {
          &.disabled {
            cursor: not-allowed;
            color: rgba(6, 21, 66, 0.2);
          }

          padding: 6px;
        }
      }

      .toolbar-right {
        display: flex;
        gap: 4px;

        .stop-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          padding: 6px;
        }

        .send-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }

        .send-icon {
          color: #4a70ff;
        }

        .stop-icon:hover,
        .send-icon:hover {
          background: rgba(18, 42, 79, 0.08);
          border-radius: 3px;
        }

        .stop-icon:active,
        .send-icon:active {
          background: #f1f6ff;
          border-radius: 3px;
        }

        .stop-icon.disabled,
        .send-icon.disabled {
          background: none;
          cursor: not-allowed;

          .v-icon {
            cursor: not-allowed;
            color: rgba(6, 21, 66, 0.2);
          }
        }

        .split-line {
          align-self: center;
          width: 1px;
          height: 16px;
          background: rgba(18, 42, 79, 0.08);
          margin: 0 5px;
        }
      }
    }
  }
}
:deep(.el-textarea) {
  border: none;
  .el-textarea__inner {
    border: none;
    border-radius: 0;
    box-shadow: none;
    background: none;
    &:focus {
      border: none;
    }
  }
}
</style>
