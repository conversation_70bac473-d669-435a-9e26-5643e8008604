<template>
  <div class="problem-container">
    <h3 class="problem-title">常见问题</h3>

    <!-- 分类标签 -->
    <div class="category-tabs">
      <div v-for="category in categories" :key="category.key" class="category-tab" :class="{ active: activeCategory === category.key }" @click="setActiveCategory(category.key)">
        {{ category.name }}
      </div>
    </div>

    <!-- 问题列表 -->
    <div class="problem-list">
      <div v-for="(item, index) in currentProblems" :key="index" class="problem-item" @click="selectProblem(item)">
        <div class="problem-number">{{ index + 1 }}</div>
        <div class="problem-text">{{ item.problem }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="Problem">
import { ref, computed, onMounted } from 'vue'

// 定义数据类型
interface ProblemItem {
  problem: string
  answer: string
}

interface Category {
  name: string
  key: string
  data: ProblemItem[]
}

// 定义 emit 事件
const emit = defineEmits<{
  selectProblem: [item: ProblemItem]
}>()

// 响应式数据
const categories = ref<Category[]>([])
const activeCategory = ref<string>('')

// 计算当前分类的问题列表
const currentProblems = computed(() => {
  const category = categories.value.find(cat => cat.key === activeCategory.value)
  return category ? category.data : []
})

// 设置活跃分类
const setActiveCategory = (key: string) => {
  activeCategory.value = key
}

// 选择问题
const selectProblem = (item: ProblemItem) => {
  console.log('选择的问题:', item.problem)
  // 向父组件传递选中的问题
  emit('selectProblem', item)
}

// 加载问题数据
const loadProblems = async () => {
  try {
    // 动态导入 JSON 文件
    const data = await import('./problem.json')
    categories.value = data.default || data

    // 设置默认活跃分类
    if (categories.value.length > 0) {
      activeCategory.value = categories.value[0].key
    }
  } catch (error) {
    console.error('加载问题数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProblems()
})
</script>

<style scoped lang="scss">
.problem-container {
  padding: 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.problem-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.category-tab {
  padding: 8px 16px;
  background: #e9ecef;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    background: #d1ecf1;
    color: #0c5460;
  }

  &.active {
    background: #17a2b8;
    color: white;
  }
}

.problem-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.problem-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    background: #f0f8ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

.problem-number {
  width: 24px;
  height: 24px;
  background: #17a2b8;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.problem-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

/* 滚动条样式 */
.problem-list::-webkit-scrollbar {
  width: 6px;
}

.problem-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.problem-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.problem-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
